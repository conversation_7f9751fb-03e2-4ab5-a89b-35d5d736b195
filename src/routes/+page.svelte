<script lang="ts">
	import PDFViewer from '$lib/PDFViewer.svelte';
</script>

<svelte:head>
	<title>PDF Text Extraction Tool</title>
	<meta name="description" content="Upload and extract text from PDF documents with interactive rectangles" />
</svelte:head>

<main>
	<header>
		<h1>PDF Text Extraction Tool</h1>
		<p>Upload a PDF document to view it with interactive text regions. Click on any highlighted text to see its content and coordinates.</p>
	</header>
	
	<PDFViewer />
</main>

<style>
	main {
		min-height: 100vh;
		background-color: #f5f5f5;
		padding: 20px 0;
	}

	header {
		text-align: center;
		margin-bottom: 40px;
		padding: 0 20px;
	}

	h1 {
		color: #333;
		font-size: 2.5rem;
		margin-bottom: 16px;
		font-weight: 600;
	}

	p {
		color: #666;
		font-size: 1.1rem;
		max-width: 600px;
		margin: 0 auto;
		line-height: 1.6;
	}

	@media (max-width: 768px) {
		h1 {
			font-size: 2rem;
		}
		
		p {
			font-size: 1rem;
			padding: 0 10px;
		}
	}
</style>
