<script lang="ts">
	import { onMount } from 'svelte';
	import { browser } from '$app/environment';

	// Dynamic import for PDF.js
	let pdfjsLib: any = null;

	interface TextItem {
		str: string;
		transform: number[];
		width: number;
		height: number;
		x: number;
		y: number;
		fontName: string;
		fontSize: number;
	}

	interface SelectedText {
		text: string;
		x: number;
		y: number;
		width: number;
		height: number;
	}

	let fileInput: HTMLInputElement;
	let canvasContainer: HTMLDivElement;
	let pdfDoc: any = null;
	let pageNum = 1;
	let pageRendering = false;
	let pageNumPending: number | null = null;
	let scale = 1.0;
	let textItems: TextItem[] = [];
	let selectedText: SelectedText | null = null;
	let showTextInfo = false;
	let isLoading = false;
	let extractedText = '';
	let activeTab = 'text'; // 'text' or 'table' or 'layout'
	let pdfPageDimensions = { width: 0, height: 0 }; // Store PDF page dimensions for layout view

	onMount(async () => {
		if (browser) {
			try {
				// Dynamically import PDF.js only on the client side
				pdfjsLib = await import('pdfjs-dist');
				
				// Configure PDF.js worker - try multiple approaches
				try {
					// Option 1: Try jsdelivr with correct URL format
					const workerUrl = `https://cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjsLib.version}/build/pdf.worker.mjs`;
					pdfjsLib.GlobalWorkerOptions.workerSrc = workerUrl;
					
					// Test if the worker URL is accessible
					const response = await fetch(workerUrl, { method: 'HEAD' });
					if (!response.ok) {
						throw new Error('Worker URL not accessible');
					}
				} catch (workerError) {
					console.warn('External worker failed, using fallback approach:', workerError);
					
					// Option 2: Disable worker and use legacy fallback (which works fine for our use case)
					pdfjsLib.GlobalWorkerOptions.workerSrc = '';
					
					// Or try a different CDN format
					try {
						pdfjsLib.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@${pdfjsLib.version}/legacy/build/pdf.worker.js`;
					} catch {
						// If all else fails, use no worker (legacy mode)
						pdfjsLib.GlobalWorkerOptions.workerSrc = '';
						console.info('Using PDF.js legacy mode without worker');
					}
				}
			} catch (error) {
				console.error('Error loading PDF.js:', error);
			}
		}
	});

	function arrangeTextByReadingOrder(items: TextItem[]): string {
		// Sort text items by reading order (top to bottom, left to right)
		const sortedItems = [...items]
			.filter(item => item.str.trim()) // Remove empty strings
			.sort((a, b) => {
				// First sort by Y position (top to bottom)
				const yDiff = a.y - b.y;
				if (Math.abs(yDiff) > 5) { // 5px tolerance for same line
					return yDiff;
				}
				// If on the same line, sort by X position (left to right)
				return a.x - b.x;
			});

		// Group items by approximate line (Y position)
		const lines: TextItem[][] = [];
		let currentLine: TextItem[] = [];
		let lastY = -1;

		sortedItems.forEach(item => {
			if (lastY === -1 || Math.abs(item.y - lastY) > 5) {
				// New line
				if (currentLine.length > 0) {
					lines.push(currentLine);
				}
				currentLine = [item];
				lastY = item.y;
			} else {
				// Same line
				currentLine.push(item);
			}
		});

		if (currentLine.length > 0) {
			lines.push(currentLine);
		}

		// Convert lines to text
		return lines
			.map(line => {
				// Sort line items by X position
				const sortedLine = line.sort((a, b) => a.x - b.x);
				
				// Join words with appropriate spacing
				let lineText = '';
				let lastX = -1;
				let lastWidth = 0;

				sortedLine.forEach(item => {
					if (lastX !== -1) {
						const gap = item.x - (lastX + lastWidth);
						if (gap > item.fontSize * 0.3) { // If gap is larger than 30% of font size
							lineText += ' '; // Add space
						}
					}
					lineText += item.str;
					lastX = item.x;
					lastWidth = item.width;
				});

				return lineText;
			})
			.filter(line => line.trim()) // Remove empty lines
			.join('\n');
	}

	async function handleFileUpload(event: Event) {
		if (!browser || !pdfjsLib) {
			alert('PDF.js is not loaded yet. Please try again.');
			return;
		}

		const target = event.target as HTMLInputElement;
		const file = target.files?.[0];
		
		if (!file || file.type !== 'application/pdf') {
			alert('Please select a valid PDF file');
			return;
		}

		isLoading = true;
		try {
			const arrayBuffer = await file.arrayBuffer();
			pdfDoc = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
			pageNum = 1;
			await renderPage(pageNum);
		} catch (error) {
			console.error('Error loading PDF:', error);
			alert('Error loading PDF file');
		} finally {
			isLoading = false;
		}
	}

	async function renderPage(num: number) {
		if (!browser || !pdfjsLib || pageRendering) {
			if (pageRendering) {
				pageNumPending = num;
			}
			return;
		}

		pageRendering = true;

		try {
			const page = await pdfDoc.getPage(num);
			const viewport = page.getViewport({ scale });

			// Store page dimensions for layout view
			pdfPageDimensions = {
				width: viewport.width,
				height: viewport.height
			};

			// Clear previous content
			canvasContainer.innerHTML = '';

			// Create and setup canvas
			const canvas = document.createElement('canvas');
			const ctx = canvas.getContext('2d');
			if (!ctx) {
				throw new Error('Could not get canvas context');
			}

			canvas.height = viewport.height;
			canvas.width = viewport.width;
			canvas.style.position = 'relative';
			canvas.style.display = 'block';

			// Render PDF page
			await page.render({
				canvasContext: ctx,
				viewport: viewport
			}).promise;
			
			// Extract text with coordinates - need to transform coordinates to match the scaled viewport
			// ROBUSTNESS IMPROVEMENTS:
			// 1. Fixed font size extraction from PDF.js transform matrix
			// 2. Added proper viewport transformation handling with fallback
			// 3. Added coordinate validation and bounds checking
			// 4. Improved Y-coordinate conversion from PDF (bottom-left) to web (top-left) coordinate system
			// 5. Added debug logging for troubleshooting
			const textContent = await page.getTextContent();
			textItems = textContent.items.map((item: any) => {
				// Validate item has required properties
				if (!item || !item.transform || !Array.isArray(item.transform) || item.transform.length < 6) {
					console.warn('Invalid text item:', item);
					return null;
				}

				const transform = item.transform;

				// Apply the viewport transformation to get correctly scaled coordinates
				// The transform matrix contains [scaleX, skewY, skewX, scaleY, translateX, translateY]
				const x = transform[4] || 0;
				const y = transform[5] || 0;

				// Get font size from transform matrix - it's the vertical scaling factor (transform[3])
				// For most cases, font size is Math.abs(transform[3]) or item.height if available
				let fontSize = item.height || Math.abs(transform[3]) || Math.abs(transform[0]) || 12;

				// Ensure we have a reasonable font size
				if (fontSize < 1) {
					fontSize = 12; // Default fallback
				}

				// Transform coordinates using the viewport transformation
				// Apply viewport transformation matrix to get screen coordinates
				// Fallback to simple scaling if viewport.transform is not available
				let scaledX, scaledY, scaledWidth, scaledHeight, scaledFontSize;

				if (viewport.transform && viewport.transform.length >= 6) {
					// Use full viewport transformation matrix
					scaledX = x * viewport.transform[0] + viewport.transform[4];
					scaledY = viewport.height - (y * viewport.transform[3] + viewport.transform[5]); // Convert to top-left origin
					scaledWidth = Math.max(item.width * viewport.transform[0], 1);
					scaledHeight = Math.max(fontSize * Math.abs(viewport.transform[3]), 1);
					scaledFontSize = Math.max(fontSize * Math.abs(viewport.transform[3]), 8);
				} else {
					// Fallback to simple scaling
					scaledX = x * scale;
					scaledY = viewport.height - (y * scale); // Convert to top-left origin and scale
					scaledWidth = Math.max(item.width * scale, 1);
					scaledHeight = Math.max(fontSize * scale, 1);
					scaledFontSize = Math.max(fontSize * scale, 8);
				}

				// Validate dimensions to ensure rectangles are visible and within bounds
				const validX = Math.max(0, Math.min(scaledX, viewport.width));
				const validY = Math.max(0, Math.min(scaledY, viewport.height));
				const validWidth = Math.max(1, Math.min(scaledWidth, viewport.width - validX));
				const validHeight = Math.max(1, Math.min(scaledHeight, viewport.height - validY));
				const validFontSize = Math.max(8, scaledFontSize);

				// Debug logging for problematic items
				if (validHeight < 1 || validWidth < 1 || validX < 0 || validY < 0) {
					console.warn('Invalid text item dimensions:', {
						text: item.str,
						originalHeight: fontSize,
						scaledHeight: validHeight,
						originalWidth: item.width,
						scaledWidth: validWidth,
						x: validX,
						y: validY,
						transform: transform,
						viewport: {
							width: viewport.width,
							height: viewport.height,
							transform: viewport.transform
						}
					});
				}

				return {
					str: item.str,
					transform: transform,
					width: validWidth,
					height: validHeight,
					x: validX,
					y: validY,
					fontName: item.fontName,
					fontSize: validFontSize
				};
			}).filter((item: any) => item !== null); // Remove invalid items

			// Update extracted text display
			extractedText = arrangeTextByReadingOrder(textItems);
			
			// Create container for canvas and overlays
			const container = document.createElement('div');
			container.style.position = 'relative';
			container.style.display = 'inline-block';
			
			// Add canvas
			container.appendChild(canvas);
			
			// Create text rectangles overlay
			const overlay = document.createElement('div');
			overlay.style.position = 'absolute';
			overlay.style.top = '0';
			overlay.style.left = '0';
			overlay.style.width = '100%';
			overlay.style.height = '100%';
			overlay.style.pointerEvents = 'none';
			
			// Add clickable rectangles for each text item
			console.log(`Rendering ${textItems.length} text items for page ${num}`);

			textItems.forEach((item) => {
				if (item.str.trim()) {
					const rect = document.createElement('div');
					rect.style.position = 'absolute';

					// Position rectangle - coordinates are already properly transformed
					const rectLeft = Math.max(0, item.x);
					const rectTop = Math.max(0, item.y - item.height);
					const rectWidth = Math.max(1, item.width);
					const rectHeight = Math.max(1, item.height);

					rect.style.left = `${rectLeft}px`;
					rect.style.top = `${rectTop}px`;
					rect.style.width = `${rectWidth}px`;
					rect.style.height = `${rectHeight}px`;

					// Visual styling
					rect.style.border = '1px solid rgba(255, 0, 0, 0.3)';
					rect.style.backgroundColor = 'rgba(255, 255, 0, 0.1)';
					rect.style.cursor = 'pointer';
					rect.style.pointerEvents = 'auto';
					rect.style.boxSizing = 'border-box';

					// Accessibility
					rect.title = item.str;
					rect.setAttribute('role', 'button');
					rect.setAttribute('tabindex', '0');
					rect.setAttribute('aria-label', `Select text: ${item.str}`);

					// Debug info as data attributes
					rect.setAttribute('data-text', item.str);
					rect.setAttribute('data-x', rectLeft.toString());
					rect.setAttribute('data-y', rectTop.toString());
					rect.setAttribute('data-width', rectWidth.toString());
					rect.setAttribute('data-height', rectHeight.toString());
					rect.setAttribute('data-original-x', item.x.toString());
					rect.setAttribute('data-original-y', item.y.toString());

					const handleClick = () => {
						selectedText = {
							text: item.str,
							x: item.x,
							y: item.y,
							width: item.width,
							height: item.height
						};
						showTextInfo = true;
					};

					rect.addEventListener('click', handleClick);
					rect.addEventListener('keydown', (e) => {
						if (e.key === 'Enter' || e.key === ' ') {
							e.preventDefault();
							handleClick();
						}
					});

					overlay.appendChild(rect);
				}
			});
			
			container.appendChild(overlay);
			canvasContainer.appendChild(container);
			
		} catch (error) {
			console.error('Error rendering page:', error);
		}
		
		pageRendering = false;
		
		if (pageNumPending !== null) {
			renderPage(pageNumPending);
			pageNumPending = null;
		}
	}

	async function onPrevPage() {
		if (pageNum <= 1) return;
		pageNum--;
		await renderPage(pageNum);
	}

	async function onNextPage() {
		if (pageNum >= pdfDoc.numPages) return;
		pageNum++;
		await renderPage(pageNum);
	}

	function closeTextInfo() {
		showTextInfo = false;
		selectedText = null;
	}

	function handleModalKeydown(event: KeyboardEvent) {
		if (event.key === 'Escape') {
			closeTextInfo();
		}
	}

	function handleModalClick() {
		closeTextInfo();
	}

	function handleModalContentClick(event: Event) {
		event.stopPropagation();
	}

	function copyTextToClipboard() {
		if (navigator.clipboard && extractedText) {
			navigator.clipboard.writeText(extractedText).then(() => {
				alert('Text copied to clipboard!');
			}).catch(() => {
				alert('Failed to copy text to clipboard');
			});
		}
	}

	function setActiveTab(tab: string) {
		activeTab = tab;
	}

	function exportTableAsCSV() {
		if (!textItems.length) return;

		const headers = ['Text', 'X', 'Y', 'Width', 'Height', 'Font Name', 'Font Size'];
		const rows = textItems
			.filter(item => item.str.trim())
			.map(item => [
				`"${item.str.replace(/"/g, '""')}"`, // Escape quotes in CSV
				Math.round(item.x).toString(),
				Math.round(item.y).toString(),
				Math.round(item.width).toString(),
				Math.round(item.height).toString(),
				item.fontName || 'Unknown',
				Math.round(item.fontSize).toString()
			]);

		const csvContent = [headers.join(','), ...rows.map(row => row.join(','))].join('\n');
		
		const blob = new Blob([csvContent], { type: 'text/csv' });
		const url = URL.createObjectURL(blob);
		const a = document.createElement('a');
		a.href = url;
		a.download = 'pdf-text-data.csv';
		a.click();
		URL.revokeObjectURL(url);
	}

	// Get sorted text items for table display
	$: sortedTextItems = textItems
		.filter(item => item.str.trim())
		.sort((a, b) => {
			const yDiff = a.y - b.y;
			if (Math.abs(yDiff) > 5) return yDiff;
			return a.x - b.x;
		});
</script>

<div class="pdf-viewer">
	<div class="upload-section">
		<input
			bind:this={fileInput}
			type="file"
			accept=".pdf"
			on:change={handleFileUpload}
			class="file-input"
			id="pdf-file-input"
			disabled={!browser || !pdfjsLib || isLoading}
		/>
		<label for="pdf-file-input" class="upload-button" class:disabled={!browser || !pdfjsLib || isLoading}>
			{#if isLoading}
				Loading...
			{:else if !browser || !pdfjsLib}
				Loading PDF.js...
			{:else}
				Choose PDF File
			{/if}
		</label>
	</div>

	{#if pdfDoc}
		<div class="controls">
			<button on:click={onPrevPage} disabled={pageNum <= 1}>Previous</button>
			<span>Page {pageNum} of {pdfDoc.numPages}</span>
			<button on:click={onNextPage} disabled={pageNum >= pdfDoc.numPages}>Next</button>
			
			<div class="zoom-controls">
				<button on:click={() => { scale = Math.max(0.5, scale - 0.25); renderPage(pageNum); }}>-</button>
				<span>Zoom: {Math.round(scale * 100)}%</span>
				<button on:click={() => { scale = Math.min(3, scale + 0.25); renderPage(pageNum); }}>+</button>
			</div>
		</div>

		<div class="main-content">
			<div class="pdf-section">
				<h3>PDF Viewer</h3>
				<div class="canvas-container" bind:this={canvasContainer}></div>
			</div>

			<div class="data-section">
				<div class="tab-header">
					<button 
						class="tab-button" 
						class:active={activeTab === 'text'}
						on:click={() => setActiveTab('text')}
					>
						Extracted Text
					</button>
					<button 
						class="tab-button" 
						class:active={activeTab === 'table'}
						on:click={() => setActiveTab('table')}
					>
						Table View
					</button>
					<button 
						class="tab-button" 
						class:active={activeTab === 'layout'}
						on:click={() => setActiveTab('layout')}
					>
						Layout View
					</button>
				</div>

				{#if activeTab === 'text'}
					<div class="tab-content">
						<div class="text-header">
							<h3>Extracted Text</h3>
							<button class="copy-button" on:click={copyTextToClipboard} disabled={!extractedText}>
								Copy Text
							</button>
						</div>
						<textarea
							class="extracted-text"
							value={extractedText}
							placeholder="Upload a PDF to see extracted text here..."
							readonly
						></textarea>
					</div>
				{:else if activeTab === 'table'}
					<div class="tab-content">
						<div class="table-header">
							<h3>Text Data Table</h3>
							<button class="export-button" on:click={exportTableAsCSV} disabled={!sortedTextItems.length}>
								Export CSV
							</button>
						</div>
						<div class="table-container">
							{#if sortedTextItems.length > 0}
								<table class="data-table">
									<thead>
										<tr>
											<th>Text</th>
											<th>X</th>
											<th>Y</th>
											<th>Width</th>
											<th>Height</th>
											<th>Font</th>
											<th>Size</th>
										</tr>
									</thead>
									<tbody>
										{#each sortedTextItems as item}
											<tr>
												<td class="text-cell" title={item.str}>{item.str}</td>
												<td>{Math.round(item.x)}</td>
												<td>{Math.round(item.y)}</td>
												<td>{Math.round(item.width)}</td>
												<td>{Math.round(item.height)}</td>
												<td class="font-cell" title={item.fontName}>{item.fontName || 'Unknown'}</td>
												<td>{Math.round(item.fontSize)}</td>
											</tr>
										{/each}
									</tbody>
								</table>
							{:else}
								<div class="empty-state">
									<p>No text data available. Upload a PDF to see the table.</p>
								</div>
							{/if}
						</div>
					</div>
				{:else if activeTab === 'layout'}
					<div class="tab-content">
						<div class="layout-header">
							<h3>Layout View</h3>
							<div class="layout-info">
								<span>Page: {pdfPageDimensions.width}×{pdfPageDimensions.height}px</span>
							</div>
						</div>
						<div class="layout-container">
							{#if sortedTextItems.length > 0 && pdfPageDimensions.width > 0}
								<div 
									class="layout-page" 
									style="width: {pdfPageDimensions.width}px; height: {pdfPageDimensions.height}px;"
								>
									{#each textItems as item}
										{#if item.str.trim()}
											<div
												class="layout-text-item"
												style="
													left: {Math.max(0, item.x)}px;
													top: {Math.max(0, item.y - item.height)}px;
													width: {Math.max(1, item.width)}px;
													height: {Math.max(1, item.height)}px;
													font-size: {Math.max(8, item.fontSize * 0.8)}px;
												"
												title="Text: '{item.str}' | Position: ({Math.round(item.x)}, {Math.round(item.y)}) | Rect: ({Math.max(0, item.x)}, {Math.max(0, item.y - item.height)}) | Font: {item.fontName} | Size: {Math.round(item.fontSize)}px"
											>
												{item.str}
											</div>
										{/if}
									{/each}
								</div>
							{:else}
								<div class="empty-state">
									<p>No layout data available. Upload a PDF to see the layout view.</p>
								</div>
							{/if}
						</div>
					</div>
				{/if}
			</div>
		</div>
	{:else}
		<div class="placeholder-content">
			<div class="canvas-container" bind:this={canvasContainer}></div>
		</div>
	{/if}

	{#if showTextInfo && selectedText}
		<!-- svelte-ignore a11y-click-events-have-key-events -->
		<!-- svelte-ignore a11y-no-static-element-interactions -->
		<div 
			class="text-info-modal" 
			on:click={handleModalClick}
			on:keydown={handleModalKeydown}
			role="dialog"
			aria-modal="true"
			aria-labelledby="modal-title"
			tabindex="-1"
		>
			<!-- svelte-ignore a11y-click-events-have-key-events -->
			<!-- svelte-ignore a11y-no-static-element-interactions -->
			<div class="text-info-content" on:click={handleModalContentClick}>
				<h3 id="modal-title">Selected Text Information</h3>
				<div class="info-grid">
					<div class="info-item">
						<span class="info-label">Text:</span>
						<span class="text-content">"{selectedText.text}"</span>
					</div>
					<div class="info-item">
						<span class="info-label">X Coordinate:</span>
						<span>{Math.round(selectedText.x)}px</span>
					</div>
					<div class="info-item">
						<span class="info-label">Y Coordinate:</span>
						<span>{Math.round(selectedText.y)}px</span>
					</div>
					<div class="info-item">
						<span class="info-label">Width:</span>
						<span>{Math.round(selectedText.width)}px</span>
					</div>
					<div class="info-item">
						<span class="info-label">Height:</span>
						<span>{Math.round(selectedText.height)}px</span>
					</div>
				</div>
				<button class="close-button" on:click={closeTextInfo}>Close</button>
			</div>
		</div>
	{/if}
</div>

<style>
	.pdf-viewer {
		max-width: 100%;
		margin: 0 auto;
		padding: 20px;
		height: 100vh;
		display: flex;
		flex-direction: column;
	}

	.upload-section {
		margin-bottom: 20px;
		text-align: center;
		flex-shrink: 0;
	}

	.file-input {
		display: none;
	}

	.upload-button {
		display: inline-block;
		padding: 12px 24px;
		background-color: #4CAF50;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-size: 16px;
		transition: background-color 0.3s;
	}

	.upload-button:hover:not(.disabled) {
		background-color: #45a049;
	}

	.upload-button.disabled {
		background-color: #ccc;
		cursor: not-allowed;
	}

	.controls {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: 20px;
		margin-bottom: 20px;
		flex-wrap: wrap;
		flex-shrink: 0;
	}

	.controls button {
		padding: 8px 16px;
		background-color: #2196F3;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		transition: background-color 0.3s;
	}

	.controls button:hover:not(:disabled) {
		background-color: #1976D2;
	}

	.controls button:disabled {
		background-color: #ccc;
		cursor: not-allowed;
	}

	.zoom-controls {
		display: flex;
		align-items: center;
		gap: 10px;
	}

	.zoom-controls button {
		padding: 4px 8px;
		min-width: 30px;
	}

	.main-content {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20px;
		flex: 1;
		min-height: 0;
	}

	.pdf-section {
		display: flex;
		flex-direction: column;
	}

	.data-section {
		display: flex;
		flex-direction: column;
	}

	.tab-header {
		display: flex;
		border-bottom: 2px solid #e0e0e0;
		margin-bottom: 10px;
	}

	.tab-button {
		padding: 10px 20px;
		background: none;
		border: none;
		border-bottom: 3px solid transparent;
		cursor: pointer;
		font-size: 14px;
		font-weight: 500;
		color: #666;
		transition: all 0.3s ease;
	}

	.tab-button.active {
		color: #2196F3;
		border-bottom-color: #2196F3;
	}

	.tab-button:hover {
		color: #2196F3;
		background-color: #f5f5f5;
	}

	.tab-content {
		display: flex;
		flex-direction: column;
		flex: 1;
		min-height: 0;
	}

	.text-header,
	.table-header,
	.layout-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10px;
	}

	.pdf-section h3,
	.text-header h3,
	.table-header h3,
	.layout-header h3 {
		margin: 0;
		color: #333;
		font-size: 1.1rem;
	}

	.copy-button,
	.export-button {
		padding: 6px 12px;
		background-color: #4CAF50;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-size: 14px;
		transition: background-color 0.3s;
	}

	.copy-button:hover:not(:disabled),
	.export-button:hover:not(:disabled) {
		background-color: #45a049;
	}

	.copy-button:disabled,
	.export-button:disabled {
		background-color: #ccc;
		cursor: not-allowed;
	}

	.canvas-container {
		overflow: auto;
		border: 1px solid #ddd;
		border-radius: 4px;
		background-color: #f9f9f9;
		flex: 1;
		min-height: 400px;
		text-align: center;
	}

	.placeholder-content {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.extracted-text {
		width: 100%;
		height: 100%;
		border: 1px solid #ddd;
		border-radius: 4px;
		padding: 15px;
		font-family: 'Courier New', monospace;
		font-size: 14px;
		line-height: 1.5;
		background-color: #fafafa;
		resize: none;
		outline: none;
		flex: 1;
	}

	.extracted-text:focus {
		border-color: #4CAF50;
		background-color: white;
	}

	.table-container {
		flex: 1;
		overflow: auto;
		border: 1px solid #ddd;
		border-radius: 4px;
		background-color: white;
	}

	.layout-container {
		flex: 1;
		overflow: auto;
		border: 1px solid #ddd;
		border-radius: 4px;
		background-color: #f8f9fa;
		position: relative;
	}

	.layout-page {
		position: relative;
		background-color: white;
		margin: 20px auto;
		box-shadow: 0 2px 8px rgba(0,0,0,0.1);
		border: 1px solid #ddd;
		min-height: 400px;
	}

	.layout-text-item {
		position: absolute;
		display: flex;
		align-items: center;
		font-family: Arial, sans-serif;
		color: #333;
		background-color: rgba(255, 255, 0, 0.1);
		border: 1px solid rgba(0, 150, 255, 0.3);
		padding: 1px 2px;
		box-sizing: border-box;
		cursor: pointer;
		overflow: hidden;
		white-space: nowrap;
		text-overflow: ellipsis;
		line-height: 1;
		transition: background-color 0.2s ease;
	}

	.layout-text-item:hover {
		background-color: rgba(255, 255, 0, 0.3);
		border-color: rgba(0, 150, 255, 0.6);
		z-index: 2;
	}

	.layout-info {
		font-size: 12px;
		color: #666;
		font-family: monospace;
	}

	.text-info-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 1000;
	}

	.text-info-content {
		background-color: white;
		padding: 24px;
		border-radius: 8px;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
		max-width: 500px;
		width: 90%;
		max-height: 80vh;
		overflow-y: auto;
	}

	.text-info-content h3 {
		margin-top: 0;
		margin-bottom: 20px;
		color: #333;
		text-align: center;
	}

	.info-grid {
		display: grid;
		gap: 12px;
		margin-bottom: 20px;
	}

	.info-item {
		display: grid;
		grid-template-columns: 120px 1fr;
		gap: 10px;
		align-items: center;
	}

	.info-label {
		font-weight: bold;
		color: #555;
	}

	.text-content {
		background-color: #f5f5f5;
		padding: 8px;
		border-radius: 4px;
		font-family: monospace;
		word-break: break-all;
	}

	.close-button {
		width: 100%;
		padding: 10px;
		background-color: #f44336;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
		font-size: 16px;
		transition: background-color 0.3s;
	}

	.close-button:hover {
		background-color: #d32f2f;
	}

	@media (max-width: 768px) {
		.pdf-viewer {
			padding: 10px;
		}
		
		.main-content {
			grid-template-columns: 1fr;
			gap: 10px;
		}
		
		.controls {
			flex-direction: column;
			gap: 10px;
		}
		
		.info-item {
			grid-template-columns: 1fr;
			gap: 4px;
		}
		
		.info-label {
			font-size: 14px;
		}

		.text-header,
		.table-header,
		.layout-header {
			flex-direction: column;
			gap: 10px;
			align-items: stretch;
		}

		.extracted-text {
			height: 50vh;
		}

		.data-table th,
		.data-table td {
			padding: 6px 4px;
			font-size: 12px;
		}

		.text-cell {
			max-width: 120px;
		}

		.font-cell {
			max-width: 80px;
		}

		.layout-page {
			margin: 10px;
			transform: scale(0.7);
			transform-origin: top left;
		}

		.layout-text-item {
			font-size: 8px !important;
		}
	}

	.data-table {
		width: 100%;
		border-collapse: collapse;
		font-size: 14px;
	}

	.data-table th {
		background-color: #f5f5f5;
		padding: 12px 8px;
		text-align: left;
		font-weight: 600;
		border-bottom: 2px solid #ddd;
		position: sticky;
		top: 0;
		z-index: 1;
	}

	.data-table td {
		padding: 8px;
		border-bottom: 1px solid #eee;
		vertical-align: top;
	}

	.data-table tr:hover {
		background-color: #f9f9f9;
	}

	.text-cell {
		max-width: 200px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
	}

	.font-cell {
		max-width: 120px;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		font-family: monospace;
		font-size: 12px;
	}

	.empty-state {
		padding: 40px;
		text-align: center;
		color: #666;
	}
</style> 