# PDF Text Extraction Tool

A SvelteKit-based web application that allows users to upload PDF documents and interact with text regions to extract text content and coordinates.

## Features

- **PDF Upload**: Upload PDF files through a simple file input
- **Interactive Text Regions**: View PDFs with highlighted rectangular regions around text
- **Text Extraction**: Click on any text region to see:
  - The actual text content
  - X and Y coordinates
  - Width and height dimensions
- **Navigation**: Navigate through multi-page PDFs
- **Zoom Controls**: Zoom in and out for better visibility
- **Responsive Design**: Works on desktop and mobile devices

## Technologies Used

- **SvelteKit**: Frontend framework
- **TypeScript**: Type-safe development
- **PDF.js**: PDF rendering and text extraction
- **Canvas API**: PDF rendering and overlay graphics

## Getting Started

### Prerequisites

- Node.js (version 16 or higher)
- npm or yarn

### Installation

1. Clone or download this project
2. Install dependencies:
```bash
npm install
```

3. Start the development server:
```bash
npm run dev
```

4. Open your browser and navigate to `http://localhost:5173`

## How to Use

1. **Upload a PDF**: Click the "Choose PDF File" button and select a PDF document
2. **View Interactive Regions**: Once uploaded, you'll see the PDF with yellow highlighted rectangles around text areas
3. **Extract Text Information**: Click on any highlighted rectangle to see a modal with:
   - The text content within that region
   - X and Y coordinates (in pixels)
   - Width and height of the text region
4. **Navigate Pages**: Use the Previous/Next buttons for multi-page PDFs
5. **Zoom**: Use the + and - buttons to zoom in and out

## Building for Production

To build the application for production:

```bash
npm run build
```

The built application will be in the `build` directory.

## Technical Details

- Uses PDF.js library for PDF parsing and rendering
- Extracts text with precise coordinate information
- Overlays clickable rectangles on top of rendered PDF content
- Responsive design with mobile-friendly interface
- TypeScript for type safety and better development experience

## Browser Compatibility

This application works in modern browsers that support:
- ES6+ JavaScript features
- Canvas API
- File API
- PDF.js library

## Troubleshooting

- **PDF not loading**: Ensure the file is a valid PDF document
- **Text rectangles not appearing**: Some PDFs may have text rendered as images, which won't show interactive regions
- **Performance issues**: Large PDFs may take time to load and process

## License

This project is open source and available under the MIT License.
